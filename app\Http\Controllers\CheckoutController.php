<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;

class CheckoutController extends Controller
{
    /**
     * Display the checkout page.
     */
    public function index(): View
    {
        \Log::info('Checkout index accessed', [
            'session_id' => Session::getId(),
            'user_id' => Auth::id(),
            'is_authenticated' => Auth::check(),
        ]);

        $cart = $this->getCart();

        \Log::info('Cart retrieved for checkout', [
            'cart_found' => $cart !== null,
            'cart_id' => $cart?->id,
            'cart_session_id' => $cart?->session_id,
            'cart_user_id' => $cart?->user_id,
            'cart_items_count' => $cart?->items->count(),
            'cart_is_empty' => $cart?->isEmpty(),
        ]);

        if (!$cart || $cart->isEmpty()) {
            \Log::warning('Checkout redirected due to empty cart', [
                'cart_exists' => $cart !== null,
                'cart_is_empty' => $cart?->isEmpty(),
            ]);
            return redirect()->route('shop.index')->with('error', 'Your cart is empty.');
        }

        // Check if all items are still available
        if (!$cart->allItemsAvailable()) {
            $unavailableItems = $cart->getUnavailableItems();
            return redirect()->route('cart.index')
                ->with('error', 'Some items in your cart are no longer available.')
                ->with('unavailable_items', $unavailableItems);
        }

        // Update prices to current prices
        $cart->updatePrices();
        
        $user = Auth::user();
        
        return view('pages.checkout.index', compact('cart', 'user'));
    }

    /**
     * Process the checkout and create an order.
     */
    public function process(Request $request)
    {
        $cart = $this->getCart();
        
        if (!$cart || $cart->isEmpty()) {
            return redirect()->route('shop.index')->with('error', 'Your cart is empty.');
        }

        // Validate checkout form
        $validated = $request->validate([
            'email' => 'required|email|max:255',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            
            // Billing Address
            'billing_address_line_1' => 'required|string|max:255',
            'billing_address_line_2' => 'nullable|string|max:255',
            'billing_city' => 'required|string|max:255',
            'billing_state' => 'required|string|max:255',
            'billing_postal_code' => 'required|string|max:10',
            'billing_country' => 'required|string|max:2',
            
            // Shipping Address
            'ship_to_different_address' => 'boolean',
            'shipping_address_line_1' => 'required_if:ship_to_different_address,true|nullable|string|max:255',
            'shipping_address_line_2' => 'nullable|string|max:255',
            'shipping_city' => 'required_if:ship_to_different_address,true|nullable|string|max:255',
            'shipping_state' => 'required_if:ship_to_different_address,true|nullable|string|max:255',
            'shipping_postal_code' => 'required_if:ship_to_different_address,true|nullable|string|max:10',
            'shipping_country' => 'required_if:ship_to_different_address,true|nullable|string|max:2',
            
            'payment_method' => 'required|in:stripe,paypal,bank_transfer',
            'terms_accepted' => 'required|accepted',
            'create_account' => 'boolean',
            'password' => 'required_if:create_account,true|nullable|string|min:8|confirmed',
        ]);

        // Check stock availability one more time
        if (!$cart->allItemsAvailable()) {
            return redirect()->route('cart.index')
                ->with('error', 'Some items in your cart are no longer available.');
        }

        DB::beginTransaction();
        
        try {
            $user = null;
            
            // Handle user creation or login
            if ($request->create_account && !Auth::check()) {
                // Check if user already exists
                $existingUser = User::where('email', $validated['email'])->first();
                if ($existingUser) {
                    return back()->withErrors(['email' => 'An account with this email already exists.']);
                }
                
                // Create new user
                $user = User::create([
                    'first_name' => $validated['first_name'],
                    'last_name' => $validated['last_name'],
                    'email' => $validated['email'],
                    'phone' => $validated['phone'],
                    'password' => Hash::make($validated['password']),
                    'email_verified_at' => now(),
                ]);
                
                Auth::login($user);
            } elseif (Auth::check()) {
                $user = Auth::user();
            }

            // Prepare shipping address
            $shippingAddress = $request->ship_to_different_address ? [
                'address_line_1' => $validated['shipping_address_line_1'],
                'address_line_2' => $validated['shipping_address_line_2'],
                'city' => $validated['shipping_city'],
                'state' => $validated['shipping_state'],
                'postal_code' => $validated['shipping_postal_code'],
                'country' => $validated['shipping_country'],
            ] : [
                'address_line_1' => $validated['billing_address_line_1'],
                'address_line_2' => $validated['billing_address_line_2'],
                'city' => $validated['billing_city'],
                'state' => $validated['billing_state'],
                'postal_code' => $validated['billing_postal_code'],
                'country' => $validated['billing_country'],
            ];

            // Create order
            $order = Order::create([
                'user_id' => $user?->id,
                'order_number' => $this->generateOrderNumber(),
                'status' => 'pending',
                'currency' => $cart->currency,
                
                // Customer Information
                'customer_email' => $validated['email'],
                'customer_first_name' => $validated['first_name'],
                'customer_last_name' => $validated['last_name'],
                'customer_phone' => $validated['phone'],
                'customer_company' => $validated['company'],
                
                // Billing Address
                'billing_address' => [
                    'address_line_1' => $validated['billing_address_line_1'],
                    'address_line_2' => $validated['billing_address_line_2'],
                    'city' => $validated['billing_city'],
                    'state' => $validated['billing_state'],
                    'postal_code' => $validated['billing_postal_code'],
                    'country' => $validated['billing_country'],
                ],
                
                // Shipping Address
                'shipping_address' => $shippingAddress,
                
                // Totals
                'subtotal' => $cart->subtotal,
                'tax_amount' => $cart->tax_amount,
                'shipping_amount' => $cart->shipping_amount,
                'discount_amount' => $cart->discount_amount,
                'total' => $cart->total,
                
                'payment_method' => $validated['payment_method'],
                'payment_status' => 'pending',
            ]);

            // Create order items
            foreach ($cart->items as $cartItem) {
                $order->items()->create([
                    'product_id' => $cartItem->product_id,
                    'product_variant_id' => $cartItem->product_variant_id,
                    'product_name' => $cartItem->product->name,
                    'product_sku' => $cartItem->sku,
                    'variant_name' => $cartItem->productVariant?->name,
                    'quantity' => $cartItem->quantity,
                    'unit_price' => $cartItem->price,
                    'total_price' => $cartItem->total,
                ]);

                // Decrease inventory
                if ($cartItem->productVariant) {
                    $cartItem->productVariant->decreaseInventory($cartItem->quantity);
                } else {
                    $cartItem->product->decreaseInventory($cartItem->quantity);
                }
            }

            // Clear the cart
            $cart->delete();

            DB::commit();

            // Redirect to payment processing
            return redirect()->route('checkout.payment', $order->uuid)
                ->with('success', 'Order created successfully! Please complete your payment.');

        } catch (\Exception $e) {
            DB::rollback();
            
            return back()->withErrors(['error' => 'An error occurred while processing your order. Please try again.']);
        }
    }

    /**
     * Display the payment page.
     */
    public function payment(Order $order): View
    {
        if ($order->payment_status !== 'pending') {
            return redirect()->route('orders.show', $order->uuid);
        }

        return view('pages.checkout.payment', compact('order'));
    }

    /**
     * Process payment via AJAX.
     */
    public function processPayment(Request $request, Order $order)
    {
        $request->validate([
            'token' => 'required|string',
        ]);

        if ($order->payment_status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'This order has already been processed.',
            ]);
        }

        $paymentService = new PaymentService();

        // Process Stripe payment
        $result = $paymentService->processStripePayment($order, $request->token, [
            'customer_ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully!',
                'redirect_url' => route('checkout.success', $order->uuid),
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['error'],
            ]);
        }
    }

    /**
     * Process payment completion.
     */
    public function paymentComplete(Request $request, Order $order)
    {
        $paymentService = new PaymentService();

        try {
            $paymentMethod = $request->input('payment_method', $order->payment_method);

            switch ($paymentMethod) {
                case 'stripe':
                    $stripeToken = $request->input('stripe_token');
                    if (!$stripeToken) {
                        return back()->withErrors(['payment' => 'Payment token is required.']);
                    }

                    $result = $paymentService->processStripePayment($order, $stripeToken);

                    if ($result['success']) {
                        return redirect()->route('checkout.success', $order->uuid)
                            ->with('success', 'Payment completed successfully!');
                    } else {
                        return back()->withErrors(['payment' => $result['error']]);
                    }
                    break;

                case 'paypal':
                    // TODO: Implement PayPal processing
                    return back()->withErrors(['payment' => 'PayPal payment not yet implemented.']);
                    break;

                default:
                    return back()->withErrors(['payment' => 'Invalid payment method.']);
            }

        } catch (\Exception $e) {
            \Log::error('Payment processing error', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);

            return back()->withErrors(['payment' => 'An error occurred while processing your payment. Please try again.']);
        }
    }

    /**
     * Display the order success page.
     */
    public function success(Order $order): View
    {
        // Ensure order belongs to current user or session
        if (Auth::check()) {
            if ($order->user_id !== Auth::id()) {
                abort(403, 'Unauthorized access to order.');
            }
        } else {
            // For guest orders, check session or allow access if payment is completed
            if ($order->payment_status !== 'paid') {
                abort(403, 'Unauthorized access to order.');
            }
        }

        $order->load(['items.product', 'items.productVariant']);

        return view('pages.checkout.success', compact('order'));
    }

    /**
     * Get the current cart.
     */
    protected function getCart(): ?ShoppingCart
    {
        if (Auth::check()) {
            return ShoppingCart::active()
                ->where('user_id', Auth::id())
                ->with(['items.product', 'items.productVariant'])
                ->first();
        } else {
            $sessionId = Session::getId();
            return ShoppingCart::active()
                ->where('session_id', $sessionId)
                ->with(['items.product', 'items.productVariant'])
                ->first();
        }
    }

    /**
     * Generate a unique order number.
     */
    protected function generateOrderNumber(): string
    {
        do {
            $orderNumber = 'ORD-' . date('Y') . '-' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        } while (Order::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }
}
