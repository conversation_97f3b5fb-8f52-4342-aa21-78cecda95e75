<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="ltr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <!-- SEO Meta Tags -->
    <title><?php echo $__env->yieldContent('title', __('common.dashboard') . ' - ' . __('common.company_name')); ?></title>
    <meta name="description" content="<?php echo $__env->yieldContent('meta_description', __('common.dashboard_description')); ?>">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Admin Components CSS -->
    <link href="<?php echo e(asset('css/admin-components.css')); ?>" rel="stylesheet">

    <!-- Additional Styles -->
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="font-inter antialiased bg-neutral-50 text-primary-900">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-accent-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>

    <div class="min-h-screen bg-neutral-50">
        <!-- Sidebar -->
        <?php echo $__env->make('partials.dashboard.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Main Content -->
        <div class="lg:ml-64 min-h-screen flex flex-col">
            <!-- Header -->
            <?php echo $__env->make('partials.dashboard.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            <!-- Page Content -->
            <main id="main-content" class="flex-1">
                <?php echo $__env->yieldContent('content'); ?>
            </main>
        </div>
    </div>

    <!-- Mobile sidebar overlay -->
    <div class="fixed inset-0 bg-primary-900 bg-opacity-75 z-40 lg:hidden hidden" id="sidebar-overlay"></div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Simple Rich Text Editor -->
    <script>
        // Simple Rich Text Editor Implementation
        class SimpleRichTextEditor {
            constructor(selector) {
                this.editors = document.querySelectorAll(selector);
                this.init();
            }

            init() {
                this.editors.forEach(textarea => {
                    this.createEditor(textarea);
                });
            }

            createEditor(textarea) {
                // Create editor container
                const editorContainer = document.createElement('div');
                editorContainer.className = 'rich-text-editor border border-gray-300 rounded-md';

                // Create toolbar
                const toolbar = document.createElement('div');
                toolbar.className = 'rich-text-toolbar border-b border-gray-300 p-2 bg-gray-50 flex flex-wrap gap-1';
                toolbar.innerHTML = `
                    <button type="button" data-command="bold" class="toolbar-btn" title="Bold">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5 3a1 1 0 000 2h1.5v10H5a1 1 0 100 2h5.5a3.5 3.5 0 001.852-6.47A3.5 3.5 0 0010.5 3H5zm3.5 2v4h2a1.5 1.5 0 000-3h-2zm0 6v4h2.5a1.5 1.5 0 000-3H8.5z"/>
                        </svg>
                    </button>
                    <button type="button" data-command="italic" class="toolbar-btn" title="Italic">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 3a1 1 0 000 2h1.264l-3.5 10H4a1 1 0 100 2h5a1 1 0 000-2H7.736l3.5-10H13a1 1 0 100-2H8z"/>
                        </svg>
                    </button>
                    <button type="button" data-command="underline" class="toolbar-btn" title="Underline">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 18h12a1 1 0 100-2H4a1 1 0 100 2zM6 4a1 1 0 011-1h6a1 1 0 110 2H7v6a3 3 0 106 0V5a1 1 0 112 0v6a5 5 0 11-10 0V4z"/>
                        </svg>
                    </button>
                    <div class="border-l border-gray-300 mx-1"></div>
                    <button type="button" data-command="insertUnorderedList" class="toolbar-btn" title="Bullet List">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 16a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
                        </svg>
                    </button>
                    <button type="button" data-command="insertOrderedList" class="toolbar-btn" title="Numbered List">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 16a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
                        </svg>
                    </button>
                    <div class="border-l border-gray-300 mx-1"></div>
                    <button type="button" data-command="justifyLeft" class="toolbar-btn" title="Align Left">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 4a1 1 0 011-1h14a1 1 0 110 2H3a1 1 0 01-1-1zM2 8a1 1 0 011-1h8a1 1 0 110 2H3a1 1 0 01-1-1zM2 12a1 1 0 011-1h14a1 1 0 110 2H3a1 1 0 01-1-1zM2 16a1 1 0 011-1h8a1 1 0 110 2H3a1 1 0 01-1-1z"/>
                        </svg>
                    </button>
                    <button type="button" data-command="justifyCenter" class="toolbar-btn" title="Align Center">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 4a1 1 0 011-1h14a1 1 0 110 2H3a1 1 0 01-1-1zM5 8a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zM2 12a1 1 0 011-1h14a1 1 0 110 2H3a1 1 0 01-1-1zM5 16a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z"/>
                        </svg>
                    </button>
                    <button type="button" data-command="justifyRight" class="toolbar-btn" title="Align Right">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 4a1 1 0 011-1h14a1 1 0 110 2H3a1 1 0 01-1-1zM7 8a1 1 0 011-1h8a1 1 0 110 2H8a1 1 0 01-1-1zM2 12a1 1 0 011-1h14a1 1 0 110 2H3a1 1 0 01-1-1zM7 16a1 1 0 011-1h8a1 1 0 110 2H8a1 1 0 01-1-1z"/>
                        </svg>
                    </button>
                `;

                // Create content area
                const contentArea = document.createElement('div');
                contentArea.className = 'rich-text-content p-3 min-h-[200px] focus:outline-none';
                contentArea.contentEditable = true;
                contentArea.innerHTML = textarea.value || '';

                // Hide original textarea
                textarea.style.display = 'none';

                // Assemble editor
                editorContainer.appendChild(toolbar);
                editorContainer.appendChild(contentArea);
                textarea.parentNode.insertBefore(editorContainer, textarea);

                // Add event listeners
                this.addEventListeners(toolbar, contentArea, textarea);
            }

            addEventListeners(toolbar, contentArea, textarea) {
                // Toolbar button clicks
                toolbar.addEventListener('click', (e) => {
                    if (e.target.closest('.toolbar-btn')) {
                        e.preventDefault();
                        const button = e.target.closest('.toolbar-btn');
                        const command = button.dataset.command;

                        contentArea.focus();
                        document.execCommand(command, false, null);
                        this.updateTextarea(contentArea, textarea);
                    }
                });

                // Content changes
                contentArea.addEventListener('input', () => {
                    this.updateTextarea(contentArea, textarea);
                });

                contentArea.addEventListener('paste', (e) => {
                    setTimeout(() => {
                        this.updateTextarea(contentArea, textarea);
                    }, 10);
                });

                // Update textarea on form submit
                const form = textarea.closest('form');
                if (form) {
                    form.addEventListener('submit', () => {
                        this.updateTextarea(contentArea, textarea);
                    });
                }
            }

            updateTextarea(contentArea, textarea) {
                textarea.value = contentArea.innerHTML;
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            if (document.querySelector('.tinymce-editor')) {
                new SimpleRichTextEditor('.tinymce-editor');
            }
        });
    </script>

    <style>
        .toolbar-btn {
            @apply p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors duration-150;
        }
        .toolbar-btn:hover {
            background-color: #f3f4f6;
        }
        .rich-text-content {
            font-family: Inter, Arial, sans-serif;
            font-size: 14px;
            line-height: 1.5;
        }
        .rich-text-content:focus {
            outline: 2px solid #3b82f6;
            outline-offset: -2px;
        }
        .rich-text-content ul, .rich-text-content ol {
            margin: 1em 0;
            padding-left: 2em;
        }
        .rich-text-content p {
            margin: 0.5em 0;
        }
        .rich-text-content strong {
            font-weight: bold;
        }
        .rich-text-content em {
            font-style: italic;
        }
        .rich-text-content u {
            text-decoration: underline;
        }
    </style>

    <!-- Scripts -->
    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- Dashboard JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const sidebar = document.getElementById('dashboard-sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            if (mobileMenuButton && sidebar && overlay) {
                mobileMenuButton.addEventListener('click', function() {
                    sidebar.classList.toggle('-translate-x-full');
                    overlay.classList.toggle('hidden');
                });

                // Close sidebar when clicking overlay
                overlay.addEventListener('click', function() {
                    sidebar.classList.add('-translate-x-full');
                    overlay.classList.add('hidden');
                });
            }

            // CSRF token for AJAX requests
            window.Laravel = {
                csrfToken: '<?php echo e(csrf_token()); ?>'
            };

            // Set up AJAX defaults
            if (typeof axios !== 'undefined') {
                axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
                axios.defaults.headers.common['X-CSRF-TOKEN'] = window.Laravel.csrfToken;
            }
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/layouts/dashboard.blade.php ENDPATH**/ ?>